/**
 * Mallorca Contractors Theme Scripts
 * 
 * @package Mallorca_Contractors
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Document ready
    $(document).ready(function() {
        initStickyHeader();
        initMegaMenu();
        initServiceCards();
        initSmoothScrolling();
        initMobileMenu();
        initContactForm();
    });

    /**
     * Initialize sticky header
     */
    function initStickyHeader() {
        var header = $('.site-header');
        var headerOffset = header.offset().top;

        $(window).scroll(function() {
            if ($(window).scrollTop() > headerOffset) {
                header.addClass('sticky');
            } else {
                header.removeClass('sticky');
            }
        });
    }

    /**
     * Initialize mega menu functionality
     */
    function initMegaMenu() {
        $('.services-menu').hover(
            function() {
                $(this).find('.mega-menu').fadeIn(200);
            },
            function() {
                $(this).find('.mega-menu').fadeOut(200);
            }
        );
    }

    /**
     * Initialize service card animations
     */
    function initServiceCards() {
        $('.service-card').hover(
            function() {
                $(this).addClass('hovered');
            },
            function() {
                $(this).removeClass('hovered');
            }
        );

        // Add intersection observer for scroll animations
        if ('IntersectionObserver' in window) {
            var observer = new IntersectionObserver(function(entries) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-in');
                    }
                });
            }, {
                threshold: 0.1
            });

            $('.service-card, .vertical-block').each(function() {
                observer.observe(this);
            });
        }
    }

    /**
     * Initialize smooth scrolling for anchor links
     */
    function initSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            var target = $(this.getAttribute('href'));
            
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 80
                }, 800);
            }
        });
    }

    /**
     * Initialize enhanced mobile menu functionality
     */
    function initMobileMenu() {
        // Enhanced mobile menu toggle
        $('.mobile-menu-toggle').on('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            var $toggle = $(this);
            var $navigation = $('.wp-block-navigation');
            var $body = $('body');
            var isOpen = $toggle.hasClass('active');

            if (isOpen) {
                // Close menu
                $toggle.removeClass('active').attr('aria-expanded', 'false');
                $navigation.removeClass('active');
                $body.removeClass('menu-open');

                // Re-enable scroll
                $body.css('overflow', '');
            } else {
                // Open menu
                $toggle.addClass('active').attr('aria-expanded', 'true');
                $navigation.addClass('active');
                $body.addClass('menu-open');

                // Disable scroll when menu is open
                $body.css('overflow', 'hidden');

                // Focus first menu item for accessibility
                setTimeout(function() {
                    $navigation.find('a:first').focus();
                }, 100);
            }
        });

        // Close menu when clicking outside
        $(document).on('click', function(e) {
            var $target = $(e.target);
            if (!$target.closest('.wp-block-navigation, .mobile-menu-toggle').length) {
                $('.mobile-menu-toggle').removeClass('active').attr('aria-expanded', 'false');
                $('.wp-block-navigation').removeClass('active');
                $('body').removeClass('menu-open').css('overflow', '');
            }
        });

        // Close menu on escape key
        $(document).on('keydown', function(e) {
            if (e.keyCode === 27 && $('.mobile-menu-toggle').hasClass('active')) {
                $('.mobile-menu-toggle').removeClass('active').attr('aria-expanded', 'false');
                $('.wp-block-navigation').removeClass('active');
                $('body').removeClass('menu-open').css('overflow', '');
                $('.mobile-menu-toggle').focus();
            }
        });

        // Handle submenu toggles on mobile
        $('.wp-block-navigation-submenu > a').on('click', function(e) {
            if ($(window).width() <= 768) {
                e.preventDefault();
                var $submenu = $(this).siblings('.wp-block-navigation__submenu-container');
                var $parent = $(this).parent();

                $parent.toggleClass('submenu-open');
                $submenu.slideToggle(300);
            }
        });
    }

    /**
     * Initialize contact form enhancements
     */
    function initContactForm() {
        // Add loading states to contact forms
        $('.contact-form').on('submit', function() {
            var $form = $(this);
            var $submitBtn = $form.find('input[type="submit"], button[type="submit"]');
            
            $submitBtn.prop('disabled', true).addClass('loading');
            
            // Re-enable after 3 seconds (adjust based on your form handling)
            setTimeout(function() {
                $submitBtn.prop('disabled', false).removeClass('loading');
            }, 3000);
        });

        // Form validation enhancements
        $('.contact-form input, .contact-form textarea').on('blur', function() {
            var $field = $(this);
            var value = $field.val().trim();
            
            if ($field.prop('required') && !value) {
                $field.addClass('error');
            } else {
                $field.removeClass('error');
            }
            
            // Email validation
            if ($field.attr('type') === 'email' && value) {
                var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    $field.addClass('error');
                } else {
                    $field.removeClass('error');
                }
            }
        });
    }

    /**
     * Initialize scroll-to-top functionality
     */
    function initScrollToTop() {
        // Add scroll to top button
        $('body').append('<button id="scroll-to-top" class="scroll-to-top" aria-label="Scroll to top">↑</button>');
        
        var $scrollBtn = $('#scroll-to-top');
        
        $(window).scroll(function() {
            if ($(window).scrollTop() > 300) {
                $scrollBtn.addClass('visible');
            } else {
                $scrollBtn.removeClass('visible');
            }
        });
        
        $scrollBtn.on('click', function() {
            $('html, body').animate({
                scrollTop: 0
            }, 600);
        });
    }

    /**
     * Initialize lazy loading for images
     */
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            var imageObserver = new IntersectionObserver(function(entries, observer) {
                entries.forEach(function(entry) {
                    if (entry.isIntersecting) {
                        var img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });

            $('.lazy').each(function() {
                imageObserver.observe(this);
            });
        }
    }

    /**
     * Initialize service page functionality
     */
    function initServicePage() {
        // Service page specific animations
        $('.service-process-step').each(function(index) {
            $(this).css('animation-delay', (index * 0.2) + 's');
        });

        // Service benefits accordion
        $('.service-benefits-toggle').on('click', function() {
            var $content = $(this).next('.service-benefits-content');
            $(this).toggleClass('active');
            $content.slideToggle(300);
        });
    }

    /**
     * Initialize performance optimizations
     */
    function initPerformanceOptimizations() {
        // Debounce scroll events
        var scrollTimer = null;
        $(window).on('scroll', function() {
            if (scrollTimer !== null) {
                clearTimeout(scrollTimer);
            }
            scrollTimer = setTimeout(function() {
                // Scroll-dependent functions here
            }, 150);
        });

        // Preload critical pages
        $('a[href^="/services"], a[href^="/contact"]').on('mouseenter', function() {
            var link = this.href;
            if (!$('link[rel="prefetch"][href="' + link + '"]').length) {
                $('<link rel="prefetch" href="' + link + '">').appendTo('head');
            }
        });
    }

    /**
     * Initialize accessibility enhancements
     */
    function initAccessibility() {
        // Focus management for mobile menu
        $('.mobile-menu-toggle').on('click', function() {
            if ($('.main-navigation').hasClass('active')) {
                $('.main-navigation a:first').focus();
            }
        });

        // Keyboard navigation for service cards
        $('.service-card').attr('tabindex', '0').on('keydown', function(e) {
            if (e.which === 13 || e.which === 32) { // Enter or Space
                e.preventDefault();
                $(this).find('a').first()[0].click();
            }
        });
    }

    // Initialize additional features
    $(window).on('load', function() {
        initScrollToTop();
        initLazyLoading();
        initServicePage();
        initPerformanceOptimizations();
        initAccessibility();
    });

    // Handle window resize
    $(window).on('resize', function() {
        // Responsive adjustments
        if ($(window).width() > 768) {
            $('.main-navigation').removeClass('active');
            $('.mobile-menu-toggle').removeClass('active');
            $('body').removeClass('menu-open');
        }

        // Adjust hero section height on mobile
        adjustMobileLayout();
    });

    /**
     * Mobile-specific layout adjustments
     */
    function adjustMobileLayout() {
        var windowWidth = $(window).width();
        var windowHeight = $(window).height();

        if (windowWidth <= 768) {
            // Adjust hero section for mobile
            $('.hero-section .wp-block-cover').css({
                'min-height': Math.min(windowHeight * 0.6, 400) + 'px'
            });

            // Ensure touch-friendly buttons
            $('.wp-block-button__link').css({
                'min-height': '44px',
                'min-width': '44px'
            });

            // Optimize service cards for mobile
            $('.service-card').css({
                'margin-bottom': '1rem'
            });
        }
    }

    /**
     * Initialize enhanced mobile optimizations
     */
    function initMobileOptimizations() {
        // Prevent zoom on input focus (iOS)
        $('input, textarea, select').attr('autocomplete', 'off');

        // Add touch class for touch devices
        if ('ontouchstart' in window || navigator.maxTouchPoints > 0) {
            $('body').addClass('touch-device');
            initTouchInteractions();
        }

        // Smooth scrolling for mobile
        if ($(window).width() <= 768) {
            $('html').css({
                '-webkit-overflow-scrolling': 'touch',
                'scroll-behavior': 'smooth'
            });
        }

        // Initial mobile layout adjustment
        adjustMobileLayout();

        // Initialize touch-friendly interactions
        initTouchFeedback();
    }

    /**
     * Initialize touch-specific interactions
     */
    function initTouchInteractions() {
        // Add touch feedback to interactive elements
        $('.wp-block-button__link, .mobile-menu-toggle, .wp-block-navigation-item a').on('touchstart', function() {
            $(this).addClass('touch-active');
        }).on('touchend touchcancel', function() {
            var $this = $(this);
            setTimeout(function() {
                $this.removeClass('touch-active');
            }, 150);
        });

        // Prevent double-tap zoom on buttons
        $('.wp-block-button__link, .mobile-menu-toggle').on('touchend', function(e) {
            e.preventDefault();
            $(this)[0].click();
        });

        // Enhanced touch scrolling
        var touchStartY = 0;
        var touchEndY = 0;

        $(document).on('touchstart', function(e) {
            touchStartY = e.originalEvent.touches[0].clientY;
        });

        $(document).on('touchmove', function(e) {
            touchEndY = e.originalEvent.touches[0].clientY;

            // Prevent overscroll bounce on iOS
            if ($('body').hasClass('menu-open')) {
                e.preventDefault();
            }
        });
    }

    /**
     * Initialize touch feedback animations
     */
    function initTouchFeedback() {
        // Add ripple effect for touch interactions
        $('.wp-block-button__link, .mobile-menu-toggle').on('touchstart', function(e) {
            var $this = $(this);
            var ripple = $('<span class="touch-ripple"></span>');
            var rect = this.getBoundingClientRect();
            var size = Math.max(rect.width, rect.height);
            var x = e.originalEvent.touches[0].clientX - rect.left - size / 2;
            var y = e.originalEvent.touches[0].clientY - rect.top - size / 2;

            ripple.css({
                width: size,
                height: size,
                left: x,
                top: y
            });

            $this.append(ripple);

            setTimeout(function() {
                ripple.remove();
            }, 600);
        });

        // Improve scroll performance on touch devices
        if ('ontouchstart' in window) {
            $(window).on('scroll touchmove', function() {
                // Throttle scroll events for better performance
                clearTimeout(window.scrollTimeout);
                window.scrollTimeout = setTimeout(function() {
                    // Scroll-dependent functions here
                }, 16); // ~60fps
            });
        }
    }

    // Initialize mobile optimizations on load
    $(window).on('load', function() {
        initMobileOptimizations();
    });

})(jQuery);
